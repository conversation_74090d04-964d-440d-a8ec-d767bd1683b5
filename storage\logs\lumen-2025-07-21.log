[2025-07-21 15:47:05] local.ERROR: Target class [App\Http\Controllers\ApiHomePageController] does not exist. {"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [App\\Http\\Controllers\\ApiHomePageController] does not exist. at D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\illuminate\\container\\Container.php:914)
[stacktrace]
#0 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\illuminate\\container\\Container.php(795): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#1 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\illuminate\\container\\Container.php(731): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array)
#2 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Application.php(327): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#3 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(326): Laravel\\Lumen\\Application->make('App\\\\Http\\\\Contro...')
#4 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(284): Laravel\\Lumen\\Application->callControllerAction(Array)
#5 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(269): Laravel\\Lumen\\Application->callActionOnArrayBasedRoute(Array)
#6 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(171): Laravel\\Lumen\\Application->handleFoundRoute(Array)
#7 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#9 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#10 D:\\Development\\laragon\\www\\laravel-lumen\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#11 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\ApiHomePageController\" does not exist at D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\illuminate\\container\\Container.php:912)
[stacktrace]
#0 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\illuminate\\container\\Container.php(912): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\illuminate\\container\\Container.php(795): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#2 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\illuminate\\container\\Container.php(731): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array)
#3 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Application.php(327): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#4 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(326): Laravel\\Lumen\\Application->make('App\\\\Http\\\\Contro...')
#5 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(284): Laravel\\Lumen\\Application->callControllerAction(Array)
#6 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(269): Laravel\\Lumen\\Application->callActionOnArrayBasedRoute(Array)
#7 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(171): Laravel\\Lumen\\Application->handleFoundRoute(Array)
#8 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#9 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#10 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#11 D:\\Development\\laragon\\www\\laravel-lumen\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#12 {main}
"} 
