[2025-07-21 15:47:05] local.ERROR: Target class [App\Http\Controllers\ApiHomePageController] does not exist. {"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [App\\Http\\Controllers\\ApiHomePageController] does not exist. at D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\illuminate\\container\\Container.php:914)
[stacktrace]
#0 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\illuminate\\container\\Container.php(795): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#1 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\illuminate\\container\\Container.php(731): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array)
#2 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Application.php(327): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#3 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(326): Laravel\\Lumen\\Application->make('App\\\\Http\\\\Contro...')
#4 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(284): Laravel\\Lumen\\Application->callControllerAction(Array)
#5 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(269): Laravel\\Lumen\\Application->callActionOnArrayBasedRoute(Array)
#6 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(171): Laravel\\Lumen\\Application->handleFoundRoute(Array)
#7 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#9 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#10 D:\\Development\\laragon\\www\\laravel-lumen\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#11 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\ApiHomePageController\" does not exist at D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\illuminate\\container\\Container.php:912)
[stacktrace]
#0 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\illuminate\\container\\Container.php(912): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\illuminate\\container\\Container.php(795): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#2 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\illuminate\\container\\Container.php(731): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array)
#3 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Application.php(327): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#4 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(326): Laravel\\Lumen\\Application->make('App\\\\Http\\\\Contro...')
#5 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(284): Laravel\\Lumen\\Application->callControllerAction(Array)
#6 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(269): Laravel\\Lumen\\Application->callActionOnArrayBasedRoute(Array)
#7 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(171): Laravel\\Lumen\\Application->handleFoundRoute(Array)
#8 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#9 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#10 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#11 D:\\Development\\laragon\\www\\laravel-lumen\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#12 {main}
"} 
[2025-07-21 15:54:24] local.ERROR: A facade root has not been set. {"exception":"[object] (RuntimeException(code: 0): A facade root has not been set. at D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\illuminate\\support\\Facades\\Facade.php:352)
[stacktrace]
#0 D:\\Development\\laragon\\www\\laravel-lumen\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php(30): Illuminate\\Support\\Facades\\Facade::__callStatic('rememberForever', Array)
#1 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Http\\Controllers\\Api\\V3\\ApiHomePageController->hero_banners(Object(Laravel\\Lumen\\Http\\Request))
#2 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\illuminate\\container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#5 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#6 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(391): Illuminate\\Container\\Container->call(Array, Array)
#7 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(356): Laravel\\Lumen\\Application->callControllerCallable(Array, Array)
#8 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(331): Laravel\\Lumen\\Application->callLumenController(Object(App\\Http\\Controllers\\Api\\V3\\ApiHomePageController), 'hero_banners', Array)
#9 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(284): Laravel\\Lumen\\Application->callControllerAction(Array)
#10 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(269): Laravel\\Lumen\\Application->callActionOnArrayBasedRoute(Array)
#11 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(171): Laravel\\Lumen\\Application->handleFoundRoute(Array)
#12 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(431): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#13 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(167): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#14 D:\\Development\\laragon\\www\\laravel-lumen\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#15 D:\\Development\\laragon\\www\\laravel-lumen\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#16 {main}
"} 
