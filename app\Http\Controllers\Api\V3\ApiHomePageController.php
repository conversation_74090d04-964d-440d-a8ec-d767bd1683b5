<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\Controller;
use App\Http\Resources\V2\ProductMiniCollection;
use App\Http\Resources\V3\BannerResource;
use App\Http\Resources\V3\FeaturedCategoryResource;
use App\Http\Resources\V3\FeaturedProductResource;

use App\Http\Resources\V3\Product\ProductVideosResource;
use App\Http\Resources\V3\ProductsResource;
use App\Http\Resources\V3\TopBrandsResource;
use App\Models\Category;
use App\Models\Product;
use App\Models\Video;
use Illuminate\Http\Request;
use App\Models\WizardInfo;
use Validator;
use Illuminate\Support\Facades\Cache;
use App\Models\RecentlyViewedProduct;

class ApiHomePageController extends ApiResponse
{
    public function hero_banners(Request $request)
    {
        $cache_key = 'home_hero_banners';
        $cache_duration = 60 * 60 * 24 * 30; // 7 days in seconds

        $data = Cache::rememberForever($cache_key, function () {
            //$data = Cache::remember($cache_key, $cache_duration, function () {
            $banners = WizardInfo::query()
                ->select(['id', 'slug', 'wizard_info_status', 'wizard_info_position', 'start_date', 'end_date'])
                ->with([
                    'wizard_details' => function ($query) {
                        $query->select([
                            'id',
                            'wizard_info_id',
                            'wizard_detail_title',
                            'wizard_detail_sub_title',
                            'wizard_detail_button_text',
                            'wizard_detail_button_link',
                            'wizard_detail_images',
                            'wizard_detail_mobile_images',
                            'wizard_detail_background_color',
                            'wizard_detail_position'
                        ])->orderBy('wizard_detail_position', 'asc');
                    }
                ])
                ->where('wizard_info_status', 1)
                ->whereIn('slug', [
                    'home-page-hero-banner-one',
                    'home-page-hero-banner-two',
                    'home-page-hero-banner-three'
                ])
                ->orderBy('wizard_info_position', 'asc')
                ->get()
                ->keyBy('slug');

            return [
                'banner_one' => \App\Http\Resources\V4\WizardDetailResource::collection(
                    $banners->get('home-page-hero-banner-one')?->wizard_details ?? collect([])
                )->resolve(),

                'banner_two' => \App\Http\Resources\V4\WizardDetailResource::collection(
                    $banners->get('home-page-hero-banner-two')?->wizard_details ?? collect([])
                )->resolve(),

                'banner_three' => \App\Http\Resources\V4\WizardDetailResource::collection(
                    $banners->get('home-page-hero-banner-three')?->wizard_details ?? collect([])
                )->resolve(),
            ];
        });

        return $this->withCacheControl($cache_duration, false)
            ->success($data);
    }

    public function hero_banners2(Request $request)
    {
        $cache_key = 'home_hero_banners2';
        $cache_duration = 60 * 60 * 24 * 30; // 30 days in seconds

        // Use Octane cache if available, otherwise fallback to Cache
        $data = (function () use ($cache_key, $cache_duration) {
            if (function_exists('octane') && app()->bound('octane')) {
                return \Laravel\Octane\Facades\Octane::cache()->rememberForever($cache_key, function () {
                    return $this->getMiddleHeroBannersData();
                });
            } else {
                return Cache::rememberForever($cache_key, function () {
                    return $this->getMiddleHeroBannersData();
                });
            }
        })();

        return $this->withCacheControl($cache_duration, false)
            ->success($data);
    }

    // Helper method for hero_banners2
    protected function getMiddleHeroBannersData()
    {
        $banners = WizardInfo::query()
            ->select(['id', 'slug', 'wizard_info_status', 'wizard_info_position', 'start_date', 'end_date'])
            ->with([
                'wizard_details' => function ($query) {
                    $query->select([
                        'id',
                        'wizard_info_id',
                        'wizard_detail_title',
                        'wizard_detail_sub_title',
                        'wizard_detail_button_text',
                        'wizard_detail_button_link',
                        'wizard_detail_images',
                        'wizard_detail_mobile_images',
                        'wizard_detail_background_color',
                        'wizard_detail_position'
                    ])->orderBy('wizard_detail_position', 'asc');
                }
            ])
            ->where('wizard_info_status', 1)
            ->whereIn('slug', [
                'home-page-middle-hero-banner-one',
                'home-page-middle-hero-banner-two',
                'home-page-middle-hero-banner-three'
            ])
            ->orderBy('wizard_info_position', 'asc')
            ->get()
            ->keyBy('slug');

        return [
            'banner_one' => \App\Http\Resources\V4\WizardDetailResource::collection(
                $banners->get('home-page-middle-hero-banner-one')?->wizard_details ?? collect([])
            )->resolve(),

            'banner_two' => \App\Http\Resources\V4\WizardDetailResource::collection(
                $banners->get('home-page-middle-hero-banner-two')?->wizard_details ?? collect([])
            )->resolve(),

            'banner_three' => \App\Http\Resources\V4\WizardDetailResource::collection(
                $banners->get('home-page-middle-hero-banner-three')?->wizard_details ?? collect([])
            )->resolve(),
        ];
    }

    public function hero_banners_tow(Request $request)
    {
        $cache_key = 'home_hero_banners_tow';
        $cache_duration = 60 * 60 * 24 * 30; // Cache for 7 days in seconds

        $data = Cache::rememberForever($cache_key, function () {
            $banners = WizardInfo::query()
                ->select(['id', 'slug', 'wizard_info_status', 'wizard_info_position', 'start_date', 'end_date'])
                ->with([
                    'wizard_details' => function ($query) {
                        $query->select([
                            'id',
                            'wizard_info_id',
                            'wizard_detail_title',
                            'wizard_detail_sub_title',
                            'wizard_detail_button_text',
                            'wizard_detail_button_link',
                            'wizard_detail_images',
                            'wizard_detail_mobile_images',
                            'wizard_detail_background_color',
                            'wizard_detail_position'
                        ])->orderBy('wizard_detail_position', 'asc');
                    }
                ])
                ->where('wizard_info_status', 1)
                ->whereIn('slug', [
                    'home-page-middle-hero-banner-one',
                    'home-page-middle-hero-banner-two',
                    'home-page-middle-hero-banner-three'
                ])
                ->orderBy('wizard_info_position', 'asc')
                ->get()
                ->keyBy('slug');

            return [
                'banner_one' => \App\Http\Resources\V4\WizardDetailResource::collection(
                    $banners->get('home-page-middle-hero-banner-one')?->wizard_details ?? collect([])
                )->resolve(),

                'banner_tow' => \App\Http\Resources\V4\WizardDetailResource::collection(
                    $banners->get('home-page-middle-hero-banner-two')?->wizard_details ?? collect([])
                )->resolve(),

                'banner_three' => \App\Http\Resources\V4\WizardDetailResource::collection(
                    $banners->get('home-page-middle-hero-banner-three')?->wizard_details ?? collect([])
                )->resolve(),
            ];
        });

        return $this->withCacheControl($cache_duration, false)
            ->success($data);
    }

    public function hero_banners_tow_bk(Request $request)
    {

        $cache_key = 'home_hero_banners_tow';
        $cache_duration = null; // Cache for 1 hour

        return Cache::rememberForever($cache_key, function () {
            // return Cache::remember($cache_key, $cache_duration, function () {
            $banners = WizardInfo::query()
                ->select('id', 'slug', 'wizard_info_status', 'wizard_info_position')
                ->with([
                    'wizard_details' => function ($query) {
                        $query->select("*")
                            ->orderBy('wizard_detail_position', 'asc');
                    }
                ])
                ->where('wizard_info_status', 1)
                ->whereIn('slug', [
                    'home-page-middle-hero-banner-one',
                    'home-page-middle-hero-banner-two',
                    'home-page-middle-hero-banner-three'
                ])
                ->orderBy('wizard_info_position', 'asc')
                ->get()
                ->keyBy('slug');

            return $this->success([
                'banner_one' => new BannerResource($banners->get('home-page-middle-hero-banner-one')?->wizard_details),
                'banner_tow' => new BannerResource($banners->get('home-page-middle-hero-banner-two')?->wizard_details),
                'banner_three' => new BannerResource($banners->get('home-page-middle-hero-banner-three')?->wizard_details)
            ]);
        });
    }


    public function promotional_features(Request $request)
    {
        $wizard_info = WizardInfo::with('wizard_details')
            ->where('slug', 'home-page-promotional-features')
            ->where('wizard_info_status', 1)
            ->orderBy('wizard_info_position', 'asc')
            ->first();
        $data['features'] = new BannerResource($wizard_info->wizard_details);
        return $this->success($data);
    }

    public function featured_products(Request $request)
    {
        $per_page = $request->input('per_page', 10);
        $page = $request->input('page', 1);

        $wizard_info = WizardInfo::with([
            'wizard_details' => function ($query) use ($per_page, $page) {
                $query->orderBy('wizard_detail_position', 'asc') // Order by wizard_detail_position
                    ->skip(($page - 1) * $per_page)
                    ->take($per_page);
            },
            'wizard_details.product'
        ])
            ->where('slug', 'home-page-featured-product')
            ->where('wizard_info_status', 1)
            ->orderBy('wizard_info_position', 'asc')
            ->first();
        // If no wizard_info found, return empty response with proper pagination
        if (!$wizard_info) {
            $data = [
                'message' => 'This Feature is Not Available',
                'products' => [],
                'pagination' => [
                    'currentPage' => (int) $page,
                    'totalPages' => 0,
                    'totalItems' => 0,
                    'itemsPerPage' => (int) $per_page,
                ]
            ];
            return $this->success($data);
        }




        $total_items = $wizard_info ? $wizard_info->wizard_details->count() : 0;
        $total_pages = ceil($total_items / $per_page);

        $data = [
            'products' => new FeaturedProductResource($wizard_info ? $wizard_info->wizard_details : null),
            'pagination' => [
                'currentPage' => (int) $page,
                'totalPages' => $total_pages,
                'totalItems' => $total_items,
                'itemsPerPage' => (int) $per_page,
            ]
        ];
        return $this->success($data);
    }

    public function featured_categories(Request $request)
    {
        $cache_key = 'home_page_featured_categories';
        $cache_duration = null; // Cache for 1 hour

        return Cache::rememberForever($cache_key, function () {
            //return Cache::remember($cache_key, $cache_duration, function () {
            $wizard_info = WizardInfo::with([
                'wizard_details' => function ($query) {
                    $query->orderBy('wizard_detail_position', 'asc'); // Order by wizard_detail_position
                },
                'wizard_details.category'
            ])
                ->where('slug', 'home-page-featured-category')
                ->where('wizard_info_status', 1)
                ->orderBy('wizard_info_position', 'asc')
                ->first();
            $data['categories'] = new FeaturedCategoryResource($wizard_info->wizard_details);
            return $this->success($data);
        });
    }
    public function on_sales_products(Request $request)
    {
        $per_page = $request->input('per_page', 10);
        $page = $request->input('page', 1);

        $wizard_info = WizardInfo::with([
            'wizard_details' => function ($query) use ($per_page, $page) {
                $query->orderBy('wizard_detail_position', 'asc') // Order by wizard_detail_position
                    ->skip(($page - 1) * $per_page)
                    ->take($per_page);
            },
            'wizard_details.product'
        ])
            ->where('slug', 'home-page-on-sales-product')
            ->where('wizard_info_status', 1)
            ->orderBy('wizard_info_position', 'asc')
            ->first();
        $total_items = $wizard_info ? $wizard_info->wizard_details->count() : 0;
        $total_pages = ceil($total_items / $per_page);

        $data = [
            'products' => new FeaturedProductResource($wizard_info ? $wizard_info->wizard_details : null),
            'pagination' => [
                'currentPage' => (int) $page,
                'totalPages' => $total_pages,
                'totalItems' => $total_items,
                'itemsPerPage' => (int) $per_page,
            ]
        ];
        return $this->success($data);
    }
    public function promo_banners(Request $request)
    {
        $wizard_info_one = WizardInfo::with([
            'wizard_details' => function ($query) {
                $query->orderBy('wizard_detail_position', 'asc'); // Order by wizard_detail_position
            },
        ])
            ->where('slug', 'home-page-secondary-promo-banners')
            ->where('wizard_info_status', 1)
            ->orderBy('wizard_info_position', 'asc')
            ->first();
        $data['banners'] = new BannerResource($wizard_info_one->wizard_details);

        return $this->success($data);
    }

    public function product_by_category(Request $request)
    {
        $per_page = $request->input('per_page', 10);
        $page = $request->input('page', 1);

        $wizard_info = WizardInfo::with([
            'wizard_details' => function ($query) use ($per_page, $page) {
                $query->orderBy('wizard_detail_position', 'asc')
                    ->skip(($page - 1) * $per_page)
                    ->take($per_page);
            },
            'wizard_details.category'
        ])
            ->where('slug', 'home-page-product-by-category')
            ->where('wizard_info_status', 1)
            ->orderBy('wizard_info_position', 'asc')
            ->first();

        return $this->success(new FeaturedCategoryResource($wizard_info->wizard_details));
    }

    public function product_by_category_slug(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'category_slug' => 'required|exists:categories,slug'
        ]);

        if ($validator->fails()) {
            return $this->error('RESOURCE_NOT_FOUND', 'The requested resource was not found', $validator->errors()->first());
        }

        $per_page = $request->input('per_page', 10);
        $page = $request->input('page', 1);
        $category_slug = $request->input('category_slug');
        $name = $request->input('name', '');

        $cache_key = 'home_page_product_by_category_slug:' . $category_slug . ':page_' . $page . ':per_page_' . $per_page . ':name_' . md5($name);
        $cache_duration = 60 * 60 * 24 * 7; // 1 hour in seconds

        return Cache::remember($cache_key, $cache_duration, function () use ($category_slug, $per_page, $page, $name) {
            $category = Category::where('slug', $category_slug)->firstOrFail(); // Ensure category exists

            $products = Product::where('category_id', $category->id)
                ->where('published', 1)
                ->physical(); // Apply the scope for physical products

            if (!empty($name)) {
                $products = $products->where('name', 'like', '%' . $name . '%');
            }

            $products = filter_products($products)
                ->orderBy('created_at', 'desc'); // Ordering products by latest

            $total_items = $products->count();
            $total_pages = ceil($total_items / $per_page);

            // Apply pagination
            $products = $products->skip(($page - 1) * $per_page)
                ->take($per_page)
                ->get();

            $data = [
                'products' => new ProductsResource($products), // Pass paginated products to resource
                'pagination' => [
                    'currentPage' => (int) $page,
                    'totalPages' => $total_pages,
                    'totalItems' => $total_items,
                    'itemsPerPage' => (int) $per_page,
                ]
            ];
            return $this->success($data);
        });
    }

    public function top_brands(Request $request)
    {
        // Define a unique cache key based on pagination parameters to cache different pages separately
        $per_page = $request->input('per_page', 10);
        $page = $request->input('page', 1);
        $cache_key = "home_top_brands_page_{$page}_per_page_{$per_page}";  // Cache key based on page and per_page
        $cache_duration = 60 * 60 * 24 * 7; // Cache for 7 days

        // Retrieve data from cache or fetch it from the database if cache is not present
        $data = Cache::remember($cache_key, $cache_duration, function () use ($per_page, $page) {
            $wizard_info = WizardInfo::with([
                'wizard_details' => function ($query) use ($per_page, $page) {
                    $query->orderBy('wizard_detail_position', 'asc') // Order by wizard_detail_position
                        ->skip(($page - 1) * $per_page)
                        ->take($per_page);
                },
                'wizard_details.product'
            ])
                ->where('slug', 'home-page-top-brands')
                ->where('wizard_info_status', 1)
                ->orderBy('wizard_info_position', 'asc')
                ->first();

            // Calculate total items and total pages for pagination
            $total_items = $wizard_info ? $wizard_info->wizard_details->count() : 0;
            $total_pages = ceil($total_items / $per_page);

            // Prepare data
            return [
                'brands' => new TopBrandsResource($wizard_info ? $wizard_info->wizard_details : collect([])),
                'pagination' => [
                    'currentPage' => (int) $page,
                    'totalPages' => $total_pages,
                    'totalItems' => $total_items,
                    'itemsPerPage' => (int) $per_page,
                ]
            ];
        });

        // Return the cached or newly fetched data
        return $this->withCacheControl($cache_duration, false)->success($data);
    }


    public function featuredVideos(Request $request)
    {
        $per_page = $request->input('per_page', 10);
        $page = $request->input('page', 1);

        $wizard_info = WizardInfo::with([
            'videos' => function ($query) use ($per_page, $page) {
                $query->orderBy('sequence', 'asc') // Order by wizard_detail_position
                    ->skip(($page - 1) * $per_page)
                    ->take($per_page);
            }
        ])
            ->where('slug', 'home-page-featured-videos')
            ->where('wizard_info_status', 1)
            ->orderBy('wizard_info_position', 'asc')
            ->first();

        $total_items = $wizard_info ? $wizard_info->videos->count() : 0;
        $total_pages = ceil($total_items / $per_page);

        $data = [
            'videos' => new ProductVideosResource($wizard_info ? $wizard_info->videos : null),
            'pagination' => [
                'currentPage' => (int) $page,
                'totalPages' => $total_pages,
                'totalItems' => $total_items,
                'itemsPerPage' => (int) $per_page,
            ]
        ];
        return $this->success($data);
    }
    public function featuredVideoView(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'video_id' => 'required|exists:videos,id'
        ]);

        if ($validator->fails()) {
            return $this->error('RESOURCE_NOT_FOUND', 'The requested resource was not found', $validator->errors()->first());
        }

        $video = Video::find($request->video_id);
        if ($video) {
            $video->increment('views');
            return $this->success(
                [],
                'Video view incremented successfully',
                200
            );
        } else {
            return $this->error(
                'RESOURCE_NOT_FOUND',
                'The requested resource was not found'
            );
        }
    }
    public function offers_products(Request $request)
    {
        $per_page = $request->input('per_page', 10);
        $page = $request->input('page', 1);

        $cache_key = 'home_page_offers_products:page_' . $page . ':per_page_' . $per_page;
        $cache_duration = 60 * 60; // 1 hour in seconds

        return \Cache::remember($cache_key, $cache_duration, function () use ($per_page, $page) {
            // Get products that are in active flash deals
            $flashDealProductIds = \App\Models\FlashDealProduct::whereHas('flash_deal', function ($query) {
                $query->where('status', 1)
                    ->where('start_date', '<=', now()->timestamp)
                    ->where('end_date', '>=', now()->timestamp);
            })->pluck('product_id');

            // Get current date/time
            $now = time(); // Get current timestamp

            // Query for products with discount or in flash deals
            $products = Product::where('published', 1)
                ->where(function ($query) use ($flashDealProductIds, $now) {
                    // Products with discount
                    $query->where(function ($q) use ($now) {
                        // Products with a valid discount
                        $q->where('discount', '>', 0)
                            ->where(function ($discountDate) use ($now) {
                            // Either no discount dates set or current date is within discount period
                            $discountDate->whereNull('discount_start_date')
                                ->orWhere(function ($date) use ($now) {
                                $date->where('discount_start_date', '<=', $now)
                                    ->where(function ($endDate) use ($now) {
                                        $endDate->where('discount_end_date', '>=', $now)
                                            ->orWhereNull('discount_end_date');
                                    });
                            });
                        });
                    })
                        // OR products that are part of flash deals
                        ->orWhereIn('id', $flashDealProductIds);
                })
                ->physical()
                ->orderBy('created_at', 'desc');

            $total_items = $products->count();
            $products = $products->skip(($page - 1) * $per_page)
                ->take($per_page)
                ->get();

            $total_pages = ceil($total_items / $per_page);

            $data = [
                'products' => new ProductsResource($products),
                'pagination' => [
                    'currentPage' => (int) $page,
                    'totalPages' => $total_pages,
                    'totalItems' => $total_items,
                    'itemsPerPage' => (int) $per_page,
                ]
            ];

            return $this->success($data);
        });
    }

    public function recently_viewed_products(Request $request)
    {
        // Get pagination parameters
        $per_page = $request->input('per_page', 10);
        $page = $request->input('page', 1);

        // Get user ID from authenticated user or session ID from request
        $user_id = auth()->check() ? auth()->id() : null;
        $session_id = $request->header('Session-Id') ?? $request->cookie('session_id') ?? $request->session_id ?? null;

        // Build the query for recently viewed products
        $query = RecentlyViewedProduct::query();

        // Filter by user if authenticated
        if ($user_id) {
            $query->where('user_id', $user_id);
        }
        // Or filter by session ID if available
        elseif ($session_id) {
            $query->where('session_id', $session_id);
        }

        // Get the product IDs from recently viewed products, ordered by most recently viewed
        $recentlyViewedProductIds = $query->orderBy('viewed_at', 'desc')
            ->pluck('product_id')
            ->unique()
            ->values();

        // Get total items before pagination
        $total_items = $recentlyViewedProductIds->count();

        // Apply pagination to product IDs
        $paginatedProductIds = $recentlyViewedProductIds->forPage($page, $per_page);

        // Fetch the actual products that are published and physical
        if ($paginatedProductIds->isNotEmpty()) {
            // Use whereIn with order preservation
            $productsQuery = Product::whereIn('id', $paginatedProductIds->toArray())
                ->where('published', 1)
                ->physical();

            // Get the products
            $products = $productsQuery->get();

            // Reorder products to match the order of recently viewed
            $orderedProducts = collect();
            foreach ($paginatedProductIds as $id) {
                $product = $products->firstWhere('id', $id);
                if ($product) {
                    $orderedProducts->push($product);
                }
            }

            $products = $orderedProducts;
        } else {
            $products = collect();
        }

        $total_pages = ceil($total_items / $per_page);

        $data = [
            'products' => new ProductsResource($products),
            'pagination' => [
                'currentPage' => (int) $page,
                'totalPages' => $total_pages,
                'totalItems' => $total_items,
                'itemsPerPage' => (int) $per_page,
            ]
        ];

        return $this->success($data);
    }



    public function settings_home_page(Request $request)
    {
        return $this->success([
            'settings' => [
                'featuredCategories' => [
                    'enabled' => (bool) WizardInfo::where('slug', 'home-page-featured-category')->first()->wizard_info_status,
                    'title' => 'Shop by Category',
                    'limit' => 6
                ],
                'featuredProducts' => [
                    'enabled' => (bool) WizardInfo::where('slug', 'home-page-featured-product')->first()->wizard_info_status,
                    'title' => 'Featured Products',
                    'limit' => 8
                ],
                'onSaleProducts' => [
                    'enabled' => (bool) WizardInfo::where('slug', 'home-page-on-sales-product')->first()->wizard_info_status,
                    'title' => 'Special Offers',
                    'limit' => 4
                ],
                'topBrands' => [
                    'enabled' => (bool) WizardInfo::where('slug', 'home-page-top-brands')->first()->wizard_info_status,
                    'title' => 'Our Brands',
                    'limit' => 8
                ],
                'featuredVideos' => [
                    'enabled' => (bool) WizardInfo::where('slug', 'home-page-featured-videos')->first()->wizard_info_status,
                    'title' => 'Product Videos',
                    'limit' => 3
                ],
                'recentlyViewed' => [
                    'enabled' => true,
                    'title' => 'Recently Viewed',
                    'limit' => 4
                ]
            ]
        ]);
    }



}
