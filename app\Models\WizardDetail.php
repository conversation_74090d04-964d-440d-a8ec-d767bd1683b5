<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WizardDetail extends Model
{
    use HasFactory;

    protected $fillable = [
        'wizard_info_id',
        'wizard_detail_title',
        'wizard_placement',
        'wizard_detail_sub_title',
        'wizard_detail_description',
        'wizard_detail_thumbnail_url',
        'wizard_detail_images',
        'wizard_detail_images_url',
        'wizard_detail_mobile_images',
        'wizard_detail_mobile_images_url',
        'wizard_detail_redirect_url',
        'wizard_detail_position',
        'wizard_detail_button_text',
        'wizard_detail_button_link',
        'wizard_detail_background_color',
        'video_url',
        'category_id',
        'product_id',
        'blog_id',
        'brand_id',
        'collection_id',
        'wizard_detail_html',
        'wizard_video_duration',
        'wizard_view_count',
        'publish_date',
    ];

    public function wizard_info()
    {
        return $this->belongsTo(WizardInfo::class);
    }

    public function category()
    {
       // return $this->belongsTo(Category::class);
    }

    public function product()
    {
        //return $this->belongsTo(Product::class);
    }

    public function blog()
    {
        //return $this->belongsTo(Blog::class);
    }

    public function brand()
    {
        //return $this->belongsTo(Brand::class);
    }

    public function collection()
    {
        //return $this->belongsTo(Collection::class);
    }

}
