<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WizardInfo extends Model
{
    use HasFactory;
    use HasFactory;
    protected $guarded = [];
    protected $fillable=[
        'wizard_info_name',
        'background_color',
        'slug',
        'wizard_info_description',
        'used_in',
        'type',
        'wizard_info_html',
        'start_date',
        'end_date',
        'wizard_info_redirect_url',
        'wizard_info_images',
        'wizard_info_images_url',
        'wizard_info_mobile_images',
        'wizard_info_mobile_images_url',
        'wizard_info_position',
        'wizard_info_status',
        'admin_note',
        'admin_view_file',
        'created_at',
        'updated_at',
        'created_by',
        'updated_by',
    ];
    public function wizard_details(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(WizardDetail::class);
    }
    public function videos(){
       // return $this->morphMany(Video::class,'subject');
    }
}
